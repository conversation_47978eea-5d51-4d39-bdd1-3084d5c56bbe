<?php
include 'config.php';

//Checks SESSION for user role
checkAdminRole(); 

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = htmlspecialchars($_POST['title']);
    $decription = htmlspecialchars($_POST['description']);
    $priority = $_POST ['priority'];

    $stmt = $pdo->prepare("INSERT INTO `tickets` (`user_id`, `title`, `description`, `priority`) VALUES (?, ?, ?, ?)");

    $stmt->execute([$_SESSION['user_id'], $title, $decription, $priority]);

    $_SESSION['messages'][] = "The ticket was successfully added.";

 
    header("Location: admin_dashboard.php");
    exit;
}

?>