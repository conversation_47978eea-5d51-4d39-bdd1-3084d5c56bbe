<?php
include 'config.php';

//Checks SESSION for user role
checkAdminRole();


if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $title = htmlspecialchars($_POST['title']);
    $content = htmlspecialchars($_POST['content']);
    $user = $_SESSION['user_id'];

    $stmt = $pdo->prepare("INSERT INTO `articles` (`author_id`, `title`, `content`) VALUES (?, ?, ?)");
    $stmt->execute([$user, $title, $content]);
    $_SESSION['messages'][] = "The article was successfully added.";
    header("Location: admin_dashboard.php");
    exit;

}

?>