<?php
// Step 1: Include config.php file
include 'config.php';

checkAdminRole();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = htmlspecialchars($_POST['full_name']);
    $email = $_POST['email'];
    $password = password_hash($_POST['password'], PASSWORD_DEFAULT); // Encrypt password
    $phone = htmlspecialchars($_POST['phone']);
    $activation_code = uniqid(); // Generate a unique id
    $role = $_POST['role'];

    // Check if the email is unique
    $stmt = $pdo->prepare("SELECT * FROM `users` WHERE `email` = ?");
    $stmt->execute([$email]);
    $userExists = $stmt->fetch();

    if ($userExists) {
        $_SESSION['messages'][] = "That email already exists. Please choose another.";
        header('Location: admin_dashboard.php');
        exit;
    } else {
        //Email is unique, proceed with inserting the new user record
        $insertStmt = $pdo->prepare("INSERT INTO `users`(`full_name`, `email`, `pass_hash`, `phone`,`activation_code`, `role`) VALUES (?, ?, ?, ?, ?, ?)");
        $insertStmt->execute([$full_name, $email, $password, $phone, $activation_code, $role]);

        $_SESSION['messages'][] = "The user account for $full_name was created. They will need to login to activate their account.";

        header('Location: admin_dashboard.php');
        exit;
    }
}
?>