<!-- BEGIN PAGE HEADER -->
<header class="container">

<!-- BEGIN MAIN NAV -->
<nav class="navbar is-fixed-top is-spaced has-shadow is-light" role="navigation" aria-label="main navigation">
    <div class="navbar-brand">
        <a class="navbar-item" href="index.php">
            <span class="icon-text">
                <span class="icon">
                    <i class="fas fa-2x fa-ghost"></i>
                </span>
                <span>&nbsp;<?= $siteName ?></span>
            </span>
        </a>
        <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false">
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
        </a>
    </div>
    <div class="navbar-menu">
        <div class="navbar-start">
            <!-- BEGIN ADMIN MENU -->
            <?php if (isset($_SESSION['loggedin']) && $_SESSION['user_role'] == 'admin') : ?>
                <div class="navbar-item has-dropdown is-hoverable">
                    <a class="navbar-link">
                        <span class="icon">
                            <i class="fas fa-user-cog"></i>
                        </span>
                        <span>Admin</span>
                    </a>
                    <div class="navbar-dropdown">
                        <a href="admin_dashboard.php" class="navbar-item">
                            Dashboard
                        </a>
                        <a href="users_manage.php" class="navbar-item">
                            Manage Users
                        </a>
                        <a href="articles.php" class="navbar-item">
                            Manage Articles
                        </a>
                        <a href="tickets.php" class="navbar-item">
                            Manage Tickets
                        </a>
                    </div>
                </div>
            <?php endif; ?>
            <!-- END ADMIN MENU -->
        </div>
        <div class="navbar-end">
            <!-- <a class="navbar-item" href="#">Contact Us</a>
            <a class="navbar-item" href="#">Login</a> -->
            <div class="navbar-item">
                <div class="buttons">
                    <a class="button is-light" href="contact.php">
                        <strong>Contact us</strong>
                    </a>
                    <?php if (isset($_SESSION['loggedin'])) : ?>
                        <a class="button is-light" href="ticket_create.php">
                            <strong>Support</strong>
                        </a>
                    <?php endif; ?>

                    
                    <!-- BEGIN USER MENU -->
                    <?php if (isset($_SESSION['loggedin'])) : ?>
                        <div class="navbar-item has-dropdown is-hoverable">
                            <a class="button navbar-link">
                                <span class="icon">
                                <i class="fas fa-user"></i>
                                </span>
                            </a>
                            <div class="navbar-dropdown">
                                <a href="profile.php" class="navbar-item">Profile</a>
                                <hr class="navbar-divider">
                                <a href="logout.php" class="navbar-item">Logout</a>
                            </div>
                        </div>
                    <?php else : ?>
                        <a href="login.php" class="button is-link">Login</a>
                    <?php endif; ?>
                    <!-- END USER MENU -->
                </div>
            </div>
        </div>
    </div>
</nav>
<!-- END MAIN NAV -->
<section class="block">
    &nbsp; <!-- this adds a little extra space between the nav and the hero -->
</section>
<?php if ($_SERVER['PHP_SELF'] == '/index.php') : ?>
  <!-- BEGIN HERO -->
  <section class="hero is-warning">
    <div class="hero-body">
      <p class="title">
        Trick or Treat
      </p>
      <p class="subtitle">
        Give me something good to eat...
      </p>
      <a href="contact.php" class="button is-medium is-success is-light is-rounded">
        <span class="icon is-large">
          <i class="fas fa-2x fa-broom"></i> 
        </span>
        <span>&nbsp;Cast a spell here</span>
      </a>
    </div>
  </section>
<!-- END HERO -->
<?php endif; ?>
 <?php if (!empty($_SESSION['messages'])) : ?>
  <section class="notification is-warning">
      <button class="delete"></button>
      <?php echo implode('<br>', $_SESSION['messages']);
            $_SESSION['messages'] = []; // Clear the user responses?>
  </section>
<?php endif; ?>

</header>
<!-- END PAGE HEADER -->

<!-- BEGIN MAIN PAGE CONTENT -->
<main class="container">