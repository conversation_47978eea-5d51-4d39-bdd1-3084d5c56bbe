-- Database schema for web3400 application
-- Run this script to create all necessary tables

-- Create users table
CREATE TABLE IF NOT EXISTS `users` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `full_name` varchar(255) NOT NULL,
    `email` varchar(255) NOT NULL UNIQUE,
    `pass_hash` varchar(255) NOT NULL,
    `phone` varchar(20),
    `sms` tinyint(1) DEFAULT 0,
    `subscribe` tinyint(1) DEFAULT 0,
    `activation_code` varchar(255),
    `user_bio` text,
    `role` enum('user', 'admin') DEFAULT 'user',
    `last_login` timestamp NULL DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
);

-- Create articles table
CREATE TABLE IF NOT EXISTS `articles` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `author_id` int(11) NOT NULL,
    `title` varchar(255) NOT NULL,
    `content` longtext NOT NULL,
    `is_published` tinyint(1) DEFAULT 0,
    `is_featured` tinyint(1) DEFAULT 0,
    `likes_count` int(11) DEFAULT 0,
    `favs_count` int(11) DEFAULT 0,
    `comments_count` int(11) DEFAULT 0,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    FOREIGN KEY (`author_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
);

-- Create tickets table
CREATE TABLE IF NOT EXISTS `tickets` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `title` varchar(255) NOT NULL,
    `description` text NOT NULL,
    `priority` enum('Low', 'Medium', 'High', 'Critical') DEFAULT 'Medium',
    `status` enum('Open', 'In Progress', 'Closed') DEFAULT 'Open',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
);

-- Create contact_us table
CREATE TABLE IF NOT EXISTS `contact_us` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `email` varchar(255) NOT NULL,
    `message` text NOT NULL,
    `submitted_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
);

-- Create user_interactions table
CREATE TABLE IF NOT EXISTS `user_interactions` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `article_id` int(11) NOT NULL,
    `interaction_type` enum('like', 'favorite', 'comment') NOT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`article_id`) REFERENCES `articles`(`id`) ON DELETE CASCADE,
    UNIQUE KEY `unique_user_article_interaction` (`user_id`, `article_id`, `interaction_type`)
);

-- Insert sample admin user (password: admin123)
INSERT INTO `users` (`full_name`, `email`, `pass_hash`, `role`, `activation_code`) VALUES 
('Admin User', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'activated - 2024-01-01 00:00:00');

-- Insert sample regular user (password: user123)
INSERT INTO `users` (`full_name`, `email`, `pass_hash`, `role`, `activation_code`) VALUES 
('John Doe', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'activated - 2024-01-01 00:00:00');

-- Insert sample articles
INSERT INTO `articles` (`author_id`, `title`, `content`, `is_published`, `is_featured`) VALUES 
(1, 'Welcome to Spooky Ghost', 'This is the first article on our spooky website. Welcome to the world of ghosts and mysteries!', 1, 1),
(1, 'The Haunted House Mystery', 'Deep in the woods stands an old Victorian house that locals claim is haunted. Many have reported strange sounds and ghostly apparitions...', 1, 1),
(1, 'Ghost Stories from Around the World', 'Every culture has its own ghost stories and supernatural beliefs. Let us explore some of the most fascinating tales from different countries...', 1, 0);
