<?php
    include 'config.php';

//We will code this together in class
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        //process the form elements
        $email = $_POST['email'];
        $password = $_POST['password'];

        //Check if user exist
        $stmt = $pdo->prepare("SELECT * FROM `users` WHERE `email` = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        

        //vars used to check the activation status and set username
        $activation_code = $user['activation_code'];
        $full_name = $user['full_name'];

        //Set account activation status
        $accountActivated = substr($activation_code, 0, 9) === 'activated' ? true : false;

        //If user exists and is activated and password is verfied
        if($user && $accountActivated && password_verify($password, $user['pass_hash'])) {

            //Update last-login date/time in db.
            $updateStmt = $pdo->prepare("UPDATE `users` SET `last_login` = NOW() WHERE `id` = ?");
            $updateResults = $updateStmt->execute([$user['id']]);

            // Set session variables for the user session
            $_SESSION['loggedin'] = true;
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['messages'][] = "Welcome back, $full_name";
            //$_SESSION['messages'][] = "Welcome back, {$user['full_name']}";
            
            //redirect to profile page or admin dashboard base on user role.
            if ($user['role'] == 'admin') {
                header('Location: admin_dashboard.php');
            } else {
                header('Location: profile.php');
            }
            exit;

        } elseif ($user && !$accountActivated) {
            //Account exists but has not been activated ->  regenerated the activation link
            $activation_link = "register.php?code=$activation_code";

            $_SESSION['messages'][] = "$full_name, your account has not been activated. To activate your account, <a href='$activation_link'>click here</a>.";

            //send them to the login page
            header('Location: login.php');
            exit;
        } else {
            //user not does have an account or password was incorrect.
            $_SESSION['messages'][] = "Invalid email or password. Please try again.";
            header('Location: login.php');
            exit;
        }
    }
?>
<?php include 'templates/head.php'; ?>
<?php include 'templates/nav.php'; ?>

    <!-- BEGIN YOUR CONTENT -->
<section class="section">
    <h1 class="title">Login</h1>
    <form class="box" action="login.php" method="post">
        <!-- Email -->
        <div class="field">
            <label class="label">Email</label>
            <div class="control">
                <input class="input" type="email" name="email" required>
             </div>
        </div>
        <!-- Password -->
        <div class="field">
            <label class="label">Password</label>
            <div class="control">
                <input class="input" type="password" name="password" required>
            </div>
        </div>
        <!-- Submit Button -->
        <div class="field">
            <div class="control">
                <button type="submit" class="button is-link">Login</button>
            </div>
        </div>
    </form>
    <a href="register.php" class="is-link"><strong>Create a new user account</strong></a>
</section>
<!-- END YOUR CONTENT -->

<?php include 'templates/footer.php'; ?>