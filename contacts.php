<?php 
    include 'config.php'; 
    checkAdminRole();

    $stmt = $pdo->prepare('SELECT * FROM contact_us ORDER BY submitted_at DESC LIMIT 5');
    $stmt->execute();
    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<?php include 'templates/head.php'; ?>
<?php include 'templates/nav.php'; ?>
<!--BEGIN YOUR CONTENT-->
<section class="section">
<h1 class="title">Messages</h1>
    <!-- Contact Table -->
    <table class="table is-bordered is-striped is-hoverable is-fullwidth">
        <!-- Table headers and rows for messages -->
        <thead>
          <tr>
               <th>Name</th>
               <th>Email</th>
               <th>Message</th>
          </tr> 
       </thead>
       <tbody>
           <!-- Loop messages from contact table dynamically-->
           <? foreach ($messages as $message) : ?>
               <tr>
                   <td><?= $message['name']?></td>
                   <td><?= $message['email']?></td>
                   <td><?= $message['message']?></td>
               </tr>
           <? endforeach ?>
       </tbody>
   </table>
</section>
<!--END YOUR CONTENT-->
<?php include 'templates/footer.php'; ?>